import { error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

function encodeVideoLink(url) {
  if (!url) return null;

  try {
    // Base64 encode
    const base64 = btoa(url);

    // Apply scrambling
    const encoded = base64
      .split('')
      .map((char) => {
        const code = char.charCodeAt(0);
        return String.fromCharCode(code + 7); // Shift forward
      })
      .reverse() // Reverse
      .join('');

    // Add signature
    return encoded + 'LC';
  } catch (error) {
    console.error('Error encoding URL:', error);
    return null;
  }
}

function createDetailedError(status, message, details = {}) {
  return error(status, {
    message,
    timestamp: new Date().toISOString(),
    details,
    stackTrace: new Error().stack
  });
}

export async function GET({ url }) {
  const id = url.searchParams.get('id');

  if (!id) {
    throw createDetailedError(400, 'Missing episode ID', {
      errorType: 'MISSING_PARAMETER'
    });
  }

  try {
    const { data: mediaQuery, error: queryError } = await supabase
      .from('anime_new')
      .select('secondary_source')
      .eq('id', id)
      .single();

    console.log('=== SECONDARY SOURCE DEBUG ===');
    console.log('Episode ID:', id);
    console.log('Database query result:', mediaQuery);
    console.log('Secondary source raw:', mediaQuery?.secondary_source);
    console.log('Secondary source type:', typeof mediaQuery?.secondary_source);
    console.log('Secondary source length:', mediaQuery?.secondary_source?.length);
    console.log('=== END SECONDARY SOURCE DEBUG ===');

    if (queryError) {
      console.error('Database query error:', queryError);
      throw createDetailedError(500, 'Database query failed', {
        errorType: 'DATABASE_ERROR',
        details: queryError
      });
    }

    // Check if secondary_source exists and is not empty
    if (!mediaQuery?.secondary_source ||
        (typeof mediaQuery.secondary_source === 'object' && Object.keys(mediaQuery.secondary_source).length === 0)) {
      console.error('Secondary source not found or empty for episode ID:', id);
      throw createDetailedError(404, 'Secondary source not found', {
        errorType: 'SOURCE_NOT_FOUND'
      });
    }

    // Parse secondary_source if it's a string
    let parsedSecondarySource = mediaQuery.secondary_source;
    if (typeof mediaQuery.secondary_source === 'string' && mediaQuery.secondary_source.trim()) {
      try {
        parsedSecondarySource = JSON.parse(mediaQuery.secondary_source);
        console.log('Parsed secondary_source from string:', parsedSecondarySource);
      } catch (e) {
        console.error('Failed to parse secondary_source JSON:', e);
        throw createDetailedError(500, 'Invalid secondary source format', {
          errorType: 'PARSE_ERROR'
        });
      }
    }

    const encodedVideoLink = encodeVideoLink(JSON.stringify(parsedSecondarySource));
    if (!encodedVideoLink) {
      throw createDetailedError(500, 'Failed to encode video link', {
        errorType: 'ENCODING_ERROR'
      });
    }
    return new Response(encodedVideoLink, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=600'
      }
    });
  } catch (err) {
    console.log(err);

    // If it's already a 404 error, pass it through
    if (err.status === 404) {
      throw err;
    }

    // Handle database not found errors
    if (err.code === 'PGRST116' || err.message?.includes('not found')) {
      throw createDetailedError(404, 'Episode not found', {
        errorType: 'EPISODE_NOT_FOUND',
        originalError: err.message
      });
    }

    // For all other errors, return 500
    throw createDetailedError(500, 'Failed to fetch secondary source', {
      errorType: 'SERVER_ERROR',
      originalError: err.message
    });
  }
}