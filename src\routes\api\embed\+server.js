// src/routes/embed/+server.js
import { createClient } from '@supabase/supabase-js';
import { json, redirect } from '@sveltejs/kit';

const supabaseUrl = 'https://zglyjsqsvevnyudbazgy.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpnbHlqc3FzdmV2bnl1ZGJhemd5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTM0ODYxNjYsImV4cCI6MjAwOTA2MjE2Nn0.H-_D56Tk5_8ebK9X700aFFI-zOPavq7ikhRNtU2njQ0';
const supabase = createClient(supabaseUrl, supabaseKey);

// Custom encoding function
function encodeVideoLink(url) {
  if (!url) return null;

  // Convert to base64 first
  const base64 = btoa(url);

  // Additional scrambling
  return base64
    .split('')
    .map(char => {
      const code = char.charCodeAt(0);
      return String.fromCharCode(code + 7); // Shift characters
    })
    .reverse() // Reverse the string
    .join('') + 'LC'; // Add a signature
}

function createErrorResponse(status, message, details = {}) {
  return new Response(
    JSON.stringify({
      error: message,
      timestamp: new Date().toISOString(),
      details
    }),
    {
      status,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

export async function GET({ url }) {
  const animeTitle = url.searchParams.get('title');
  const animeId = url.searchParams.get('id');
  const animeEpisode = Number(url.searchParams.get('episode'));

  // Special redirects
  if (animeTitle) {
    if (url.searchParams.get('_Monster_Season_-_Zankoku_Douwa:_Utsukushi-hime') === '') {
      return redirect(302, `https://www.lycoris.cafe/embed?id=180332&episode=1`);
    }

    if (animeTitle === 'Na_Nare_Hana_Nare' && animeEpisode === 1) {
      return redirect(302, `https://www.lycoris.cafe/embed?title=Nanare_Hananare&episode=1`);
    }

    if (animeTitle === 'NieR:Automata_Ver1.1a_Part_2' && animeEpisode === 1) {
      return redirect(302, `https://www.lycoris.cafe/embed?title=NieR_Automata_Ver1.1a_Part_2&episode=1`);
    }

    if (animeTitle === '2.5_Jigen_no_Ririsa' && animeEpisode === 1) {
      return redirect(302, `https://www.lycoris.cafe/embed?title=2-5_Jigen_no_Ririsa&episode=${animeEpisode}`);
    }

    if (animeTitle === '2.5-jigen_no_Ririsa' && animeEpisode === 13) {
      return redirect(302, `https://www.lycoris.cafe/embed?title=2-5_Jigen_no_Ririsa&episode=${animeEpisode}`);
    }

    if (animeTitle === '2.5-Jigen_no_Ririsa' && animeEpisode < 16) {
      return redirect(302, `https://www.lycoris.cafe/embed?title=2-5_Jigen_no_Ririsa&episode=${animeEpisode}`);
    }

    if (animeTitle === '2.5-Jigen_no_Ririsa' && animeEpisode > 16) {
      return redirect(302, `https://www.lycoris.cafe/embed?title=2_5_Jigen_no_Ririsa&episode=${animeEpisode}`);
    }

    if (animeTitle === 'Monogatari_Series_Off_and_Monster_Season') {
      return redirect(302, `https://www.lycoris.cafe/embed?title=Monogatari_Series_Off_&_Monster_Season&episode=${animeEpisode}`);
    }
  }

  // Validate required parameters
  if ((!animeTitle && !animeId) || !animeEpisode) {
    return redirect(302, `/`);
  }

  try {
    const episodeInfo = await getVideoLinksFromTitleOrIdAndEpisode(animeTitle, animeId, animeEpisode);
    if (!episodeInfo) {
      return createErrorResponse(404, 'Odcinek nie znaleziony', {
        searchParams: {
          title: animeTitle,
          id: animeId,
          episode: animeEpisode
        },
        errorType: 'EPISODE_NOT_FOUND'
      });
    }

    const nextEpisodeData = await getNextEpisode(animeTitle, animeId, animeEpisode);

    return json(
      {
        episodeInfo,
        nextEpisodeData
      },
      {
        headers: {
          'Cache-Control': 'public, max-age=600'
        }
      }
    );
  } catch (err) {
    console.error('Error loading episode data:', err);

    // Handle 404 errors
    if (err.status === 404) {
      return createErrorResponse(404, err.message || 'Odcinek nie znaleziony', err.details || {});
    }

    // Handle database not found errors as 404
    if (err.code === 'PGRST116' || err.message?.includes('not found')) {
      return createErrorResponse(404, 'Odcinek nie znaleziony', {
        errorType: 'EPISODE_NOT_FOUND',
        searchParams: {
          title: animeTitle,
          id: animeId,
          episode: animeEpisode
        },
        originalError: {
          message: err.message,
          name: err.name,
          code: err.code
        }
      });
    }

    // For all other errors, return 500
    return createErrorResponse(500, 'Wystąpił niespodziewany błąd', {
      errorType: 'SERVER_ERROR',
      searchParams: {
        title: animeTitle,
        id: animeId,
        episode: animeEpisode
      },
      originalError: {
        message: err.message,
        name: err.name,
        code: err.code
      }
    });
  }
}

async function getVideoLinksFromTitleOrIdAndEpisode(title, id, episode) {
  let mediaQuery;
  let queryError;

  if (id) {
    // ID-based search (existing functionality)
    const { data, error } = await supabase
      .from('anime_new')
      .select(`
              id,
              anilist_id,
              episode_title,
              episode_number,
              burst_source,
              primary_source,
              secondary_source,
              thumbnail_link,
              markerPeriods,
              thumbnailFile,
              subtitleLinks,
              date_added,
              translating_group,
              player_source,
              external_player_link
          `)
      .eq('anilist_id', id)
      .eq('episode_number', episode);

    mediaQuery = data;
    queryError = error;
  } else if (title) {
    // Title-based search - normalize the title and search in anime_metadata
    const normalizedSearchTitle = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');

    // First, find the anime by normalized title in anime_metadata
    const { data: metadataResults, error: metadataError } = await supabase
      .from('anime_metadata')
      .select('anilist_id, romaji_title, english_title')
      .eq('hidden', false);

    if (metadataError) {
      console.error('Error fetching anime metadata:', metadataError);
      throw {
        status: 500,
        message: 'Zapytanie do bazy danych nie powiodło się',
        details: {
          errorType: 'DATABASE_ERROR',
          details: metadataError
        }
      };
    }

    // Find matching anime by normalized title
    let matchingAnime = null;
    if (metadataResults) {
      for (const anime of metadataResults) {
        const normalizedRomaji = anime.romaji_title ?
          anime.romaji_title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '') : '';
        const normalizedEnglish = anime.english_title ?
          anime.english_title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '') : '';

        if (normalizedRomaji === normalizedSearchTitle || normalizedEnglish === normalizedSearchTitle) {
          matchingAnime = anime;
          break;
        }
      }
    }

    if (!matchingAnime) {
      return null;
    }

    // Now get the episode data using the found anilist_id
    const { data, error } = await supabase
      .from('anime_new')
      .select(`
              id,
              anilist_id,
              episode_title,
              episode_number,
              burst_source,
              primary_source,
              secondary_source,
              thumbnail_link,
              markerPeriods,
              thumbnailFile,
              subtitleLinks,
              date_added,
              translating_group,
              player_source,
              external_player_link
          `)
      .eq('anilist_id', matchingAnime.anilist_id)
      .eq('episode_number', episode);

    mediaQuery = data;
    queryError = error;
  } else {
    return null;
  }

  if (queryError) {
    console.error('Supabase query error:', queryError);

    // Handle database not found errors as 404
    if (queryError.code === 'PGRST116' || queryError.message?.includes('not found')) {
      throw {
        status: 404,
        message: 'Odcinek nie znaleziony',
        details: {
          errorType: 'EPISODE_NOT_FOUND',
          searchParams: {
            title: title,
            id: id,
            episode: episode
          },
          originalError: queryError
        }
      };
    }

    // For all other database errors, return 500
    throw {
      status: 500,
      message: 'Zapytanie do bazy danych nie powiodło się',
      details: {
        errorType: 'DATABASE_ERROR',
        details: queryError
      }
    };
  }

  if (!mediaQuery || mediaQuery.length === 0) {
    return null;
  }

  // If we have an anilist_id, check if the anime is hidden
  if (mediaQuery[0].anilist_id) {
    const { data: metadataQuery, error: metadataError } = await supabase
      .from('anime_metadata')
      .select('hidden')
      .eq('anilist_id', mediaQuery[0].anilist_id)
      .single();

    if (!metadataError && metadataQuery && metadataQuery.hidden === true) {
      throw {
        status: 404,
        message: 'Odcinek nie znaleziony',
        details: {
          searchParams: {
            title: title,
            id: id,
            episode: episode
          },
          errorType: 'EPISODE_NOT_FOUND'
        }
      };
    }
  }

  // Determine which source to use: secondary_source first, then primary_source
  const videoSource = mediaQuery[0].secondary_source && Object.keys(mediaQuery[0].secondary_source).length > 0
    ? mediaQuery[0].secondary_source
    : mediaQuery[0].primary_source;

  return {
    id: mediaQuery[0].id,
    anime_title: mediaQuery[0].anime_title,
    anilistId: mediaQuery[0].anilist_id,
    FHD: encodeVideoLink(videoSource ? videoSource.FHD : ''),
    HD: encodeVideoLink(videoSource ? videoSource.HD : ''),
    SD: encodeVideoLink(videoSource ? videoSource.SD : ''),
    Source: encodeVideoLink(videoSource ? videoSource.Source : ''),
    airDate: mediaQuery[0].date_added,
    burstSource: mediaQuery[0].burst_source,
    thumb: mediaQuery[0].thumbnail_link,
    markerPeriods: mediaQuery[0].markerPeriods,
    thumbnailFile: mediaQuery[0].thumbnailFile,
    PL: mediaQuery[0].subtitleLinks ? mediaQuery[0].subtitleLinks.PL : '',
    number: mediaQuery[0].episode_number,
    title: mediaQuery[0].episode_title ? mediaQuery[0].episode_title : `Odcinek ${mediaQuery[0].episode_number}`
  };
}

async function getNextEpisode(title, id, episode) {
  let anilistId = id;

  // If we have a title instead of ID, find the anilist_id first
  if (!id && title) {
    const normalizedSearchTitle = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');

    const { data: metadataResults, error: metadataError } = await supabase
      .from('anime_metadata')
      .select('anilist_id, romaji_title, english_title')
      .eq('hidden', false);

    if (metadataError) {
      console.error('Error fetching anime metadata:', metadataError);
      return null;
    }

    // Find matching anime by normalized title
    let matchingAnime = null;
    if (metadataResults) {
      for (const anime of metadataResults) {
        const normalizedRomaji = anime.romaji_title ?
          anime.romaji_title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '') : '';
        const normalizedEnglish = anime.english_title ?
          anime.english_title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '') : '';

        if (normalizedRomaji === normalizedSearchTitle || normalizedEnglish === normalizedSearchTitle) {
          matchingAnime = anime;
          break;
        }
      }
    }

    if (!matchingAnime) {
      return null;
    }

    anilistId = matchingAnime.anilist_id;
  }

  // First check if the anime is hidden
  if (anilistId) {
    const { data: metadataCheck, error: metadataError } = await supabase
      .from('anime_metadata')
      .select('hidden')
      .eq('anilist_id', anilistId)
      .single();

    if (!metadataError && metadataCheck && metadataCheck.hidden === true) {
      // If anime is hidden, return null (no next episode)
      return null;
    }
  }

  // Query to get the next episode
  const { data: nextEpisodeData, error: queryError } = await supabase
    .from('anime_new')
    .select(`
            thumbnail_link,
            anilist_id,
            episode_number
        `)
    .eq('anilist_id', anilistId)
    .eq('episode_number', Number(episode) + 1);

  if (queryError) {
    console.error('Supabase query error:', queryError);
    throw {
      status: 500,
      message: 'Zapytanie do bazy danych nie powiodło się',
      details: {
        errorType: 'DATABASE_ERROR',
        details: queryError
      }
    };
  }

  if (nextEpisodeData && nextEpisodeData.length !== 0) {
    // Check if the anime is hidden (if we have anilist_id)
    if (nextEpisodeData[0].anilist_id) {
      const { data: metadataQuery, error: metadataError } = await supabase
        .from('anime_metadata')
        .select('hidden')
        .eq('anilist_id', nextEpisodeData[0].anilist_id)
        .single();

      if (!metadataError && metadataQuery && metadataQuery.hidden === true) {
        return null;
      }
    }

    const nextEpisode = nextEpisodeData[0];
    const queryParam = title ? `title=${title}` : `id=${nextEpisode.anilist_id}`;

    // Get anime title for display
    const { data: animeMetadata } = await supabase
      .from('anime_metadata')
      .select('romaji_title')
      .eq('anilist_id', nextEpisode.anilist_id)
      .single();

    const animeTitle = animeMetadata?.romaji_title || 'Unknown Anime';

    return {
      title: `${animeTitle.replaceAll('_', ' ')} - ${nextEpisode.episode_number.toString().padStart(2, '0')}`,
      poster: nextEpisode.thumbnail_link,
      link: `https://www.lycoris.cafe/embed?${queryParam}&episode=${nextEpisode.episode_number}`
    };
  }
  return null;
}